name: build-ios-app
on:
  push:
    branches:
      - 'main'
env:
  MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
  MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
jobs:
  build:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'yarn'
    - uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '16.2.0'
    - name: Install dependencies
      run: |
        yarn install
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 3.2
        bundler-cache: true
    - name: Install Fastlane
      run: |
        cd ios
        bundle install
        bundle update fastlane
        gem install xcode-install
    - name: Install pods
      run: |
        cd ios
        pod install
    - name: Build .ipa
      env:
        FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
      run: |
        cd ios && bundle exec fastlane build_upload 