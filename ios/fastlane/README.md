# Fastlane Setup for THINKWallet

This directory contains the fastlane configuration for THINKWallet iOS app.

## Prerequisites

1. **Ruby 3.2+** - Required for fastlane
2. **Xcode 16.2+** - Latest Xcode version
3. **CocoaPods** - For iOS dependencies
4. **Apple Developer Account** - For certificates and provisioning profiles

## Setup

1. Install Ruby dependencies:
   ```bash
   cd ios
   bundle install
   ```

2. Install iOS dependencies:
   ```bash
   pod install
   ```

## Available Lanes

### `build_upload`
Builds the app for ad-hoc distribution and creates a GitHub release.
- Uses ad-hoc provisioning profile
- Increments version and build numbers
- Creates GitHub release with IPA file

### `build_only`
Builds the app for development distribution.
- Uses development provisioning profile
- Increments version and build numbers
- Creates GitHub release with IPA file

### `auth_only`
Registers devices and tests authentication.
- Registers devices from devices.txt
- Tests Apple Developer API authentication

### `increase_version`
Increments version and build numbers only.
- Updates version in Xcode project
- Commits and pushes changes

## Configuration

- **App Identifier**: `org.thinkglobalschool.wallet`
- **Apple Developer Account**: `<EMAIL>`
- **Certificates Repository**: `https://github.com/THINKGlobalSchool/THINKapps-certificates.git`
- **AuthKey**: `AuthKey_632Y79Z8TX.p8`

## GitHub Secrets Required

- `MATCH_PASSWORD`: Password for certificates repository
- `MATCH_GIT_BASIC_AUTHORIZATION`: Base64 encoded GitHub credentials
- `FASTLANE_PASSWORD`: Apple Developer account password

## Usage

### Local Development
```bash
cd ios
bundle exec fastlane build_only
```

### CI/CD
The workflows are automatically triggered on:
- `master` branch: Builds and releases ad-hoc IPA
- `develop` branch: Builds and releases development IPA 