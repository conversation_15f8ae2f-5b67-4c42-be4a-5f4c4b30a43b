# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Build & upload to S3"
  lane :build_upload do
    setup_ci if ENV['CI']
    xcode_select "/Applications/Xcode_16.2.app"

    match(type: 'adhoc', app_identifier: 'org.thinkglobalschool.wallet')

    xcodes(
      version: '16.2.0',
      select_for_current_build_only: true,
    )

    api_key = app_store_connect_api_key(
      key_id: "632Y79Z8TX",
      issuer_id: "69a6de70-a494-47e3-e053-5b8c7c11a4d1",
      key_filepath: "./AuthKey_632Y79Z8TX.p8",
      duration: 1200, # optional (maximum 1200)
      in_house: false # optional but may be required if using match/sigh
    )

    register_devices(
      devices_file: "../devices.txt",
      api_key: api_key
    )
    
    version = get_version_number()
    increment_version_number(version_number: version)
    updated_version = increment_version_number()

    # Increment the build number
    increment_build_number
    #Get Build Number
    build_number = get_build_number

    gym(
      scheme: "THINKWallet",
      configuration: "Release",
      xcargs: "-allowProvisioningUpdates",
      output_name: "THINKWallet.ipa",
      output_directory: "../build/",
      export_method: "ad-hoc",
    )

    #Set Github Release
    is_prerelease = false

    name =  "v#{updated_version}-#{build_number}"

    commit_version_bump(
      message: "Release version #{updated_version}-#{build_number}",
      xcodeproj: './THINKWallet.xcodeproj',
      force: true
    )

    current_branch = ENV['GITHUB_HEAD_REF'] || ENV['GITHUB_REF']&.split('/')&.last || git_branch

    push_to_git_remote(
      remote: "origin",
      local_branch: current_branch,
      remote_branch: current_branch,
      force: false,
      tags: true
    )

    set_github_release(
      repository_name: "THINKGlobalSchool/THINK-Wallet-prod",
      api_token: "****************************************",
      name: name,
      tag_name: "v#{updated_version}-#{build_number}",
      description: ("New devices"),
      commitish: "main",
      upload_assets: ["../build/THINKWallet.ipa"]
    )
  end
  
  # Build only step for use in local testing
  lane :build_only do
    setup_ci if ENV['CI']

    api_key = app_store_connect_api_key(
      key_id: "632Y79Z8TX",
      issuer_id: "69a6de70-a494-47e3-e053-5b8c7c11a4d1",
      key_filepath: "./AuthKey_632Y79Z8TX.p8",
      duration: 1200, # optional (maximum 1200)
      in_house: false # optional but may be required if using match/sigh
    )

    xcode_select "/Applications/Xcode_16.2.app"

    match(type: 'development', app_identifier: "org.thinkglobalschool.wallet", api_key: api_key)
    
    xcodes(
      version: '16.2.0',
      select_for_current_build_only: true,
    )

    register_devices(
      devices_file: "../devices.txt",
      api_key: api_key
    )
    
    version = get_version_number()
    increment_version_number(version_number: version)
    updated_version = increment_version_number()

    # Increment the build number
    increment_build_number
    #Get Build Number
    build_number = get_build_number
    
    gym(
      scheme: "THINKWallet",
      configuration: "Debug",
      xcargs: "-allowProvisioningUpdates",
      output_name: "THINKWallet.ipa",
      output_directory: "../build/",
      export_method: "development",
    )

    #Set Github Release
    is_prerelease = false

    name =  "v#{updated_version}-#{build_number}"

    commit_version_bump(
      message: "Release version #{updated_version}-#{build_number}",
      xcodeproj: './THINKWallet.xcodeproj',
      force: true
    )

    current_branch = ENV['GITHUB_HEAD_REF'] || ENV['GITHUB_REF']&.split('/')&.last || git_branch

    push_to_git_remote(
      remote: "origin",
      local_branch: current_branch,
      remote_branch: current_branch,
      force: false,
      tags: true
    )

    set_github_release(
      repository_name: "THINKGlobalSchool/THINK-Wallet-prod",
      api_token: "****************************************",
      name: name,
      tag_name: "v#{updated_version}-#{build_number}",
      description: ("New devices DEV"),
      commitish: "main",
      upload_assets: ["../build/THINKWallet.ipa"]
    )
  end

  # Register devices and test auth
  lane :auth_only do
    match(type: 'adhoc', app_identifier: 'org.thinkglobalschool.wallet')

    api_key = app_store_connect_api_key(
      key_id: "632Y79Z8TX",
      issuer_id: "69a6de70-a494-47e3-e053-5b8c7c11a4d1",
      key_filepath: "./AuthKey_632Y79Z8TX.p8",
      duration: 1200, # optional (maximum 1200)
      in_house: false # optional but may be required if using match/sigh
    )

    register_devices(
      devices_file: "../devices.txt",
      api_key: api_key
    )
  end

  # Increment Version and Build Number
  lane :increase_version do
    def tag_exists?(tag_name)
      system("git fetch --tags")
      `git tag -l #{tag_name}`.strip != ""
    end
    
    # Increment version number if the tag already exists
    version = get_version_number()
    increment_version_number(version_number: version)
    updated_version = increment_version_number()
    # Increment the build number
    increment_build_number
    build_number = get_build_number

    puts "version is: #{version}"
    puts "updated_version is: #{updated_version}"
    puts "build_number is: #{build_number}"
    # show latest tag with puts
    puts "Latest tag:"
    system("git describe --tags --abbrev=0")
    
    begin
      tag_name = "v#{updated_version}-#{build_number}"
      if tag_exists?(tag_name)
        # Increment the version number until a unique tag is found
        loop do
          updated_version = increment_version_number()
          puts "new updated_version is: #{updated_version}"
          tag_name = "v#{updated_version}-#{build_number}"
          break unless tag_exists?(tag_name)
        end
      end
    rescue => e
      puts "Error during version bump: #{e.message}"
    end

    puts "The new version tag is: #{tag_name}"

    commit_version_bump(
      message: "Increase version #{updated_version}-#{build_number}",
      xcodeproj: './THINKWallet.xcodeproj',
      force: true
    )

    current_branch = ENV['GITHUB_HEAD_REF'] || ENV['GITHUB_REF']&.split('/')&.last || git_branch

    push_to_git_remote(
      remote: "origin",
      local_branch: current_branch,
      remote_branch: current_branch,
      force: false,
      tags: true
    )
  end
end 