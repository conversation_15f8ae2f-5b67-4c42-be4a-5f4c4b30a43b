# THINK Wallet - Think Global School Expense Management

A React Native Expo application for managing student expenses and cash transactions at Think Global School.

## Features

### 🔐 Authentication & Authorization
- **Google OAuth Integration**: Secure sign-in with Google accounts
- **Domain Restriction**: Only @thinkglobalschool.com emails allowed
- **Role-based Access Control**: Student, Staff, Admin roles
- **Advisor Permissions**: Special privileges for staff advisors
- **Mock Google Authentication**: Demo system for development

### 💰 Transaction Management
- **Cash Given**: Staff/Admin can give cash to students
- **Cash Spent**: Record and track expenses with receipts
- **Cash Returned**: Return unused cash to the school
- Draft saving functionality
- Receipt image capture and storage

### ✅ Approval Workflow
- Pending transaction approvals for advisors/admins
- Approve/reject transactions with reasons
- Real-time status updates

### 📊 Dashboard & Analytics
- Personal expense tracking
- Transaction statistics
- Recent transaction overview
- Quick action buttons

### 🏷️ Categorization
- GL Code classification
- Program Code assignment
- Comprehensive category management

## Google Authentication Demo

The app uses Google OAuth for authentication with domain restrictions:

### Authentication Flow
1. **Click "Sign in with Google"** - Initiates OAuth flow
2. **Domain Validation** - Only @thinkglobalschool.com emails accepted
3. **User Mapping** - Google account mapped to internal user roles
4. **Secure Session** - JWT-like session management

**Note**: In development, the Google sign-in is mocked for demo purposes. Any @thinkglobalschool.com email will work.

## Technology Stack

- **Framework**: React Native with Expo (Prebuilt)
- **Language**: TypeScript
- **Authentication**: Google Sign-In (@react-native-google-signin/google-signin)
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation v6
- **Forms**: React Hook Form with Yup validation
- **UI Components**: Custom components with consistent design
- **Image Handling**: Expo Image Picker
- **Date Handling**: date-fns
- **Build**: Expo Prebuild (ios/ and android/ folders generated)

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── cards/          # Transaction and approval cards
│   ├── common/         # Basic UI components (Button, Input, etc.)
│   └── forms/          # Form components and pickers
├── constants/          # App constants (colors, roles, etc.)
├── navigation/         # Navigation configuration
├── screens/           # Screen components
│   ├── auth/          # Authentication screens
│   ├── dashboard/     # Dashboard screen
│   ├── transactions/  # Transaction management screens
│   └── approvals/     # Approval workflow screens
├── store/             # Redux store configuration
│   ├── slices/        # Redux slices
│   └── mockData/      # Mock data for demo
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (for testing)

### Installation

1. Clone the repository
2. Navigate to the TGSWallet directory
3. Install dependencies:
   ```bash
   npm install
   ```

4. **Google Sign-In Setup** (for production):
   - Create a project in [Google Cloud Console](https://console.cloud.google.com/)
   - Enable Google Sign-In API
   - Create OAuth 2.0 credentials for:
     - Web application (for web builds)
     - iOS application (for iOS builds)
     - Android application (for Android builds)
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Replace the placeholder files in the project root
   - Update `src/services/googleAuth.ts` with your client IDs

5. Start the development server:
   ```bash
   npx expo start
   ```

6. **Run on Device/Simulator**:
   - **Web**: Press `w` in the terminal or visit the web URL
   - **iOS Simulator**: Press `i` in the terminal (requires Xcode)
   - **Android Emulator**: Press `a` in the terminal (requires Android Studio)
   - **Physical Device**: Use Expo Go app to scan QR code

### Prebuild Information

The app has been prebuilt with native iOS and Android folders:
- ✅ **ios/** folder generated with Google Sign-In integration
- ✅ **android/** folder generated with Google Sign-In integration
- ✅ **CocoaPods** installed for iOS dependencies
- ✅ **Google Services** files configured (placeholders included)

**Note**: The demo uses mock Google authentication, so no actual Google setup is required for testing.

## Key Features Implementation

### Role-Based Access Control
- Students can create expense and return transactions
- Staff with advisor privileges can approve transactions and give cash
- Admins have full access to all features
- Permission checks throughout the app

### Transaction Workflow
1. **Creation**: Users create transactions with required details
2. **Validation**: Form validation ensures data integrity
3. **Submission**: Transactions are submitted for approval
4. **Approval**: Advisors/admins review and approve/reject
5. **Tracking**: All transactions are tracked with status updates

### Form Validation
- Comprehensive validation using Yup schemas
- Real-time form validation feedback
- Required field enforcement
- Amount and format validation

### Image Management
- Camera and gallery integration
- Receipt capture and storage
- Image preview and management

## Development Best Practices

- **TypeScript**: Full type safety throughout the application
- **Component Architecture**: Reusable, well-structured components
- **State Management**: Centralized state with Redux Toolkit
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive Design**: Mobile-first design approach
- **Code Organization**: Clear separation of concerns
- **Mock Data**: Realistic mock data for development and testing

## Future Enhancements

- Real API integration
- Offline support with data synchronization
- Push notifications for approvals
- Advanced reporting and analytics
- Multi-currency support
- Expense categories customization
- Bulk operations
- Export functionality

## Contributing

This is a demo application showcasing modern React Native development practices with TypeScript, Redux, and comprehensive form handling.
