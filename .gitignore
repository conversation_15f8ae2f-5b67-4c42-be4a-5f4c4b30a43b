# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
.idea/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
# *.p8  # Commented out to allow AuthKey_632Y79Z8TX.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*

# typescript
*.tsbuildinfo
