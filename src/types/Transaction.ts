export enum TransactionType {
  TRANSFER = 'transfer',
  SPEND = 'spend',
  RETURNED = 'return',
  DEPOSIT = 'deposit'
}

export enum TransactionStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export interface TransactionUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  school_class?: string;
}

export interface TransactionCategory {
  code: string;
  name: string;
  type: 'GL' | 'Program';
  parent: string;
}

export interface Transaction {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  description: string;
  purpose: string;
  glCode: string;
  glName: string;
  programCode: string;
  programName: string;
  receiptUri?: string;
  receiptUrl?: string;
  createdBy: string;
  recipient: string;
  splitWith: string[];
  createdAt: string;
  updatedAt: string;
  approvedBy: string | null;
  rejectionReason: string | null;
  user: string;
  requiresApproval: boolean;
}
