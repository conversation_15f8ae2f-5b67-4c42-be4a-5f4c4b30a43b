export interface Category {
  code: string;
  name: string;
  description: string;
  type: 'GL' | 'Program';
  user_type: string | null;
  is_active: boolean;
  created_at: string;
  parent: string | null;
}

export interface GLCode {
  id: string;
  code: string;
  description: string;
  isActive: boolean;
}

export interface ProgramCode {
  id: string;
  code: string;
  description: string;
  isActive: boolean;
}
