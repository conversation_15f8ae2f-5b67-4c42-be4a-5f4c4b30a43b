export enum UserRole {
  STUDENT = 'student',
  STAFF = 'staff', 
  ADMIN = 'admin'
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  school_class?: string | null;
  // Legacy fields for backward compatibility
  name?: string;
  role?: UserRole;
  isAdvisor?: boolean;
  assignedStudents?: string[]; // student IDs for advisors
  groups?: { name: string }[]; // Add this line for group info from API
}
