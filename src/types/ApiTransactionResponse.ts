// src/types/ApiTransactionResponse.ts

export interface ApiUser {
  email: string;
  first_name: string;
  id: string;
  last_name: string;
  school_class?: string;
}

export interface ApiCode {
  code: string;
  name: string;
}

export type ApiUserField = ApiUser | string | null;

export interface ApiTransactionResponse {
  id: string;
  type: 'spend' | 'transfer' | 'return' | 'deposit';
  status: 'pending' | 'approved' | 'rejected' | 'draft' | 'cancelled';
  amount: string;
  currency: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  approved_by?: ApiUserField;
  user: ApiUserField;
  related_user?: ApiUserField;
  requires_approval: boolean;
  split_with: ApiUserField[];
  gl_code?: ApiCode;
  program_code?: ApiCode;
  receipt?: string | null;
  receipt_url?: string;
}

// User API response (getCurrentUser)
export interface ApiCurrentUserResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  school_class?: string | null;
  groups?: { name: string }[];
  // Add more fields as per Swagger if needed
}

// Patch Transaction request (partial fields)
export type ApiPatchTransactionRequest = Partial<{
  type: 'spend' | 'transfer' | 'return' | 'deposit';
  amount: string;
  currency: string;
  notes?: string;
  gl_code?: string;
  program_code?: string;
  receipt?: string;
  related_user?: string;
  split_with?: string[];
  status?: 'pending' | 'approved' | 'rejected' | 'draft' | 'cancelled';
  // Add more fields as per Swagger if needed
}>;

// Patch Transaction response is usually the same as ApiTransactionResponse 

// PATCH Transaction payload for EditTransactionScreen
export type ApiPatchTransactionPayload = {
  submit?: boolean;
  cancel?: boolean;
  notes?: string;
  amount?: string;
  receipt?: string;
  gl_code?: string;
  program_code?: string;
}; 