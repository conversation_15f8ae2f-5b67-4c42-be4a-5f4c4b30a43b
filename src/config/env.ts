import Constants from 'expo-constants';

interface AppConfig {
  apiBaseUrl: string;
  googleIosClientId: string;
  appEnv: string;
  debugMode: boolean;
}

// Get configuration from Expo Constants (which reads from app.config.js)
const getConfig = (): AppConfig => {
  const extra = Constants.expoConfig?.extra || {};
  
  return {
    apiBaseUrl: extra.apiBaseUrl || 'https://tgswallet-backend.onrender.com',
    googleIosClientId: extra.googleIosClientId || '450493643192-1d6j385s208d5d0sofkmd8lr1tvda9pu.apps.googleusercontent.com',
    appEnv: extra.appEnv || 'development',
    debugMode: extra.debugMode === 'true' || extra.debugMode === true || __DEV__,
  };
};

export const config = getConfig();

// API Endpoints
export const API_ENDPOINTS = {
  GOOGLE_AUTH: `${config.apiBaseUrl}/users/auth/google/`,
  REFRESH_TOKEN: `${config.apiBaseUrl}/users/auth/refresh/`,
  CATEGORIES: `${config.apiBaseUrl}/categories/`,
  USERS: `${config.apiBaseUrl}/users/`,
  TRANSACTIONS: `${config.apiBaseUrl}/transactions/`,
  LEDGER_STATS: `${config.apiBaseUrl}/ledgers/stats/`,
  DEFAULT_CURRENCY: `${config.apiBaseUrl}/config/default_currency/`,
  STORAGE_UPLOAD: `${config.apiBaseUrl}/storage/upload/`,
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  GOOGLE_USER_DATA: 'google_user_data',
} as const;

// Debug logging
export const debugLog = (message: string, data?: any) => {
  if (config.debugMode) {
    console.log(`[THINK Wallet Debug] ${message}`, data || '');
  }
};
