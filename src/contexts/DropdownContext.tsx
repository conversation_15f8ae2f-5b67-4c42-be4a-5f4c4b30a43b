import React, { createContext, useContext, ReactNode } from 'react';
import { useDropdownManager, DropdownManager } from '../hooks/useDropdownManager';

const DropdownContext = createContext<DropdownManager | null>(null);

interface DropdownProviderProps {
  children: ReactNode;
}

export function DropdownProvider({ children }: DropdownProviderProps) {
  const dropdownManager = useDropdownManager();

  return (
    <DropdownContext.Provider value={dropdownManager}>
      {children}
    </DropdownContext.Provider>
  );
}

export function useDropdownContext(): DropdownManager {
  const context = useContext(DropdownContext);
  if (!context) {
    throw new Error('useDropdownContext must be used within a DropdownProvider');
  }
  return context;
}
