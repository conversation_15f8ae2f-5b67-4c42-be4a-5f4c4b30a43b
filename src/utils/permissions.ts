import { UserRole } from '../types/User';
import { TransactionType } from '../types/Transaction';

export const canCreateTransaction = (userRole: UserRole, transactionType: TransactionType): boolean => {
  switch (transactionType) {
    case TransactionType.TRANSFER:
      return userRole === UserRole.STAFF || userRole === UserRole.ADMIN;
    case TransactionType.SPEND:
    case TransactionType.RETURNED:
      return userRole === UserRole.STUDENT || userRole === UserRole.STAFF || userRole === UserRole.ADMIN;
    default:
      return false;
  }
};

export const canApproveTransactions = (userRole: UserRole, isAdvisor?: boolean): boolean => {
  if (userRole === UserRole.ADMIN) return true;
  return userRole === UserRole.STAFF && isAdvisor === true;
};