// src/utils/mapping.ts
import { TransactionType, TransactionStatus } from '../types/Transaction';
import { ApiTransactionResponse, ApiUserField, ApiCurrentUserResponse } from '../types/ApiTransactionResponse';
import { Transaction } from '../types/Transaction';
import { User, UserRole } from '../types/User';

export function extractUserField(field: Api<PERSON>ser<PERSON>ield): string {
  if (!field) return '';
  if (typeof field === 'object') return field.email || '';
  if (typeof field === 'string') return field;
  return '';
}

export function mapTransactionResponse(t: ApiTransactionResponse): Transaction {
  const typeMap: Record<string, TransactionType> = {
    spend: TransactionType.SPEND,
    transfer: TransactionType.TRANSFER,
    return: TransactionType.RETURNED,
    deposit: TransactionType.DEPOSIT,
  };
  const statusMap: Record<string, TransactionStatus> = {
    pending: TransactionStatus.PENDING,
    approved: TransactionStatus.APPROVED,
    rejected: TransactionStatus.REJECTED,
    draft: TransactionStatus.DRAFT,
    cancelled: TransactionStatus.CANCELLED,
  };
  const type = typeMap[t.type] ?? TransactionType.SPEND;
  const status = statusMap[t.status] ?? TransactionStatus.PENDING;
  const glCode = t['gl_code']?.code || '';
  const glName = t['gl_code']?.name || '';
  const programCode = t['program_code']?.code || '';
  const programName = t['program_code']?.name || '';
  const recipient = extractUserField(t['related_user']);
  const createdByUser = extractUserField(t['user']) || t['created_by'] || '';
  const approvedByUser = t['approved_by'] ? extractUserField(t['approved_by']) : null;
  const splitWith: string[] = Array.isArray(t.split_with)
    ? t.split_with.map(extractUserField)
    : [];
  // Instead of extracting just a string, map the user field to a TransactionUser object if possible
  let user: any;
  if (t.user && typeof t.user === 'object') {
    user = {
      id: t.user.id,
      email: t.user.email,
      first_name: t.user.first_name,
      last_name: t.user.last_name,
      school_class: t.user.school_class,
    };
  } else if (typeof t.user === 'string') {
    user = t.user;
  } else {
    user = '';
  }
  const rejectionReason = t['rejected_reason'] ?? null;
  return {
    id: t.id,
    type,
    status,
    amount: parseFloat(t.amount),
    currency: t.currency,
    description: t.notes || '',
    purpose: t.notes || '',
    glCode,
    glName,
    programCode,
    programName,
    receiptUri: t['receipt_url'] || t['receipt'] || undefined,
    receiptUrl: t['receipt_url'] || undefined,
    createdBy: createdByUser,
    recipient,
    splitWith,
    createdAt: t.created_at,
    updatedAt: t.updated_at,
    approvedBy: approvedByUser,
    rejectionReason,
    user,
    requiresApproval: t['requires_approval'],
  };
}

export function mapCurrentUserResponse(apiUser: ApiCurrentUserResponse): User {
  let role: UserRole | undefined = undefined;
  let isAdvisor = false;
  if (Array.isArray(apiUser.groups)) {
    const groupNames = apiUser.groups.map((g) => g.name);
    if (groupNames.includes('Student')) {
      role = UserRole.STUDENT;
    } else if (groupNames.includes('Advisor')) {
      role = UserRole.STAFF;
      isAdvisor = true;
    } else if (groupNames.includes('Admin')) {
      isAdvisor = true;
      role = UserRole.ADMIN;
    }
  }
  return {
    id: apiUser.id,
    email: apiUser.email,
    first_name: apiUser.first_name,
    last_name: apiUser.last_name,
    school_class: apiUser.school_class ?? null,
    name: apiUser.first_name + ' ' + apiUser.last_name,
    role,
    isAdvisor,
    // assignedStudents can be mapped if present in the API
  };
} 