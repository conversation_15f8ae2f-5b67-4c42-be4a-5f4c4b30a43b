import * as yup from 'yup';
import { TransactionType } from '../types/Transaction';

export const transactionSchema = (type: TransactionType) => {
  const baseSchema = {
    amount: yup.string().required('Amount is required').test('is-positive', 'Amount must be positive', value => {
      const num = parseFloat(value || '0');
      return !isNaN(num) && num > 0;
    }),
    glCode: yup.string().required('GL Code is required'),
    programCode: yup.string().required('Program Code is required'),
    splitWith: yup.array().of(yup.string()).optional().default([]),
    receiptUri: yup.string().optional(),
    description: yup.string().optional(),
    purpose: yup.string().optional(),
    recipient: yup.string().optional(),
  };

  switch (type) {
    case TransactionType.TRANSFER:
      return yup.object({
        ...baseSchema,
        recipient: yup.string().required('Recipient is required'),
        purpose: yup.string().optional(),
      });

    case TransactionType.SPEND:
      return yup.object({
        ...baseSchema,
        description: yup.string().required('Description is required'),
        receiptUri: yup.string().required('Receipt is required'),
      });

    case TransactionType.RETURNED:
      return yup.object({
        ...baseSchema,
        description: yup.string().required('Description is required'),
        // receiptUri: yup.string().optional(),
      });

    default:
      return yup.object(baseSchema);
  }
};

// Login schema removed - now using Google OAuth authentication

export function extractApiErrorMessage(error: any): string {
  // Handle case where details is directly an array of strings
  if (error && Array.isArray(error.details) && error.details.length > 0) {
    if (typeof error.details[0] === 'string' && error.details[0].trim() !== '') {
      return error.details[0];
    }
  }

  // Handle case where details is an object with keys containing arrays
  if (error && error.details && typeof error.details === 'object' && !Array.isArray(error.details)) {
    for (const key in error.details) {
      if (
        Array.isArray(error.details[key]) &&
        error.details[key].length > 0 &&
        typeof error.details[key][0] === 'string'
      ) {
        return error.details[key][0];
      }
    }
  }

  // Handle case where message exists and is meaningful
  if (typeof error?.message === 'string' && error.message.trim() !== '') {
    // Skip generic HTTP status messages that don't provide useful info
    if (!error.message.match(/^HTTP \d+:\s*$/)) {
      return error.message;
    }
  }

  return 'An unexpected error occurred.';
}
