import { TransactionType } from '../types/Transaction';

/**
 * Determines if a TRANSFER transaction is "Cash Given" or "Cash Received"
 * based on whether it was created by the current user.
 * 
 * @param transaction - The transaction object
 * @param currentUserEmail - The current user's Email
 * @returns true if it's "Cash Received", false if it's "Cash Given"
 */
export const isCashReceived = (transaction: any, currentUserEmail: string): boolean => {
  if (transaction.type !== TransactionType.TRANSFER) {
    return false;
  }
  // Get the creator ID from various possible fields
  const creatorId = transaction.createdBy || 
                   transaction.created_by || 
                   (transaction.user?.id) || 
                   transaction.user;
  
  // If transaction was NOT created by current user, it's "Cash Received"
  return creatorId !== currentUserEmail;
};

/**
 * Gets the display title for a transaction, handling Cash Given/Received logic
 * 
 * @param transaction - The transaction object
 * @param currentUserEmail - The current user's Email
 * @returns The display title for the transaction
 */
export const getTransactionDisplayTitle = (transaction: any, currentUserEmail: string): string => {
  switch (transaction.type) {
    case TransactionType.TRANSFER:
      return isCashReceived(transaction, currentUserEmail) ? 'Cash Received' : 'Cash Given';
    case TransactionType.SPEND:
      return 'Cash Spent';
    case TransactionType.RETURNED:
      return 'Cash Returned';
    case TransactionType.DEPOSIT:
      return 'Deposit';
    default:
      return 'Transaction';
  }
};

/**
 * Gets the amount prefix for a transaction, handling Cash Given/Received logic
 * 
 * @param transaction - The transaction object
 * @param currentUserEmail - The current user's Email
 * @returns The prefix ('+' or '-') for the amount display
 */
export const getTransactionAmountPrefix = (transaction: any, currentUserEmail: string): string => {
  switch (transaction.type) {
    case TransactionType.TRANSFER:
      return isCashReceived(transaction, currentUserEmail) ? '+' : '-';
    case TransactionType.SPEND:
      return '-';
    case TransactionType.RETURNED:
    case TransactionType.DEPOSIT:
      return '+';
    default:
      return '';
  }
};

/**
 * Checks if a transaction matches the given filter type, handling Cash Given/Received logic
 * 
 * @param transaction - The transaction object
 * @param filterType - The filter type to check against
 * @param currentUserEmail - The current user's Email
 * @returns true if the transaction matches the filter
 */
export const matchesTransactionFilter = (
  transaction: any, 
  filterType: string, 
  currentUserEmail: string
): boolean => {
  if (filterType === 'all') return true;
  
  switch (filterType) {
    case 'spend':
      return transaction.type === TransactionType.SPEND;
    case 'transfer':
      return transaction.type === TransactionType.TRANSFER && !isCashReceived(transaction, currentUserEmail);
    case 'cash_received':
      return transaction.type === TransactionType.TRANSFER && isCashReceived(transaction, currentUserEmail);
    case 'returned':
      return transaction.type === TransactionType.RETURNED;
    case 'deposit':
      return transaction.type === TransactionType.DEPOSIT;
    default:
      return transaction.type === filterType;
  }
};
