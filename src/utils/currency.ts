export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

export const getCurrencySymbol = (currency: string): string => {
  switch (currency) {
    case 'USD':
      return '$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    case 'JPY':
      return '¥';
    case 'CNY':
      return '¥';
    case 'INR':
      return '₹';
    case 'KRW':
      return '₩';
    case 'RUB':
      return '₽';
    case 'AUD':
      return 'A$';
    case 'CAD':
      return 'C$';
    case 'SGD':
      return 'S$';
    case 'HKD':
      return 'HK$';
    case 'THB':
      return '฿';
    case 'VND':
      return '₫';
    default:
      return currency;
  }
};