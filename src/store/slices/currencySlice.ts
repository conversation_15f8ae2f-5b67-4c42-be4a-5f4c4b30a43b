import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CurrencyState {
  value: string;
}

const initialState: CurrencyState = {
  value: 'USD', // fallback default
};

const currencySlice = createSlice({
  name: 'currency',
  initialState,
  reducers: {
    setCurrency: (state, action: PayloadAction<string>) => {
      state.value = action.payload;
    },
  },
});

export const { setCurrency } = currencySlice.actions;
export default currencySlice.reducer; 