import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../../types/User';
import { GoogleUser } from '../../services/googleAuth';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitializing: boolean;
  error: string | null;
  googleUser: GoogleUser | null;
  tokens: {
    accessToken: string;
    refreshToken: string;
  } | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  isInitializing: true, // Start as true to check for stored session
  error: null,
  googleUser: null,
  tokens: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    initializeStart: (state) => {
      state.isInitializing = true;
      state.error = null;
    },
    initializeSuccess: (state, action: PayloadAction<{ user: User; googleUser: GoogleUser; tokens: any }>) => {
      state.user = action.payload.user;
      state.googleUser = action.payload.googleUser;
      state.tokens = action.payload.tokens;
      state.isAuthenticated = true;
      state.isInitializing = false;
      state.error = null;
    },
    initializeFailure: (state) => {
      state.user = null;
      state.googleUser = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.isInitializing = false;
      state.error = null;
    },
    googleLoginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    googleLoginSuccess: (state, action: PayloadAction<{ user: User; googleUser: GoogleUser; tokens: any }>) => {
      state.user = action.payload.user;
      state.googleUser = action.payload.googleUser;
      state.tokens = action.payload.tokens;
      state.isAuthenticated = true;
      state.isLoading = false;
      state.isInitializing = false;
      state.error = null;
    },
    googleLoginFailure: (state, action: PayloadAction<string>) => {
      state.user = null;
      state.googleUser = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.isInitializing = false;
      state.error = action.payload;
    },
    updateTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string }>) => {
      state.tokens = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.googleUser = null;
      state.tokens = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.isInitializing = false;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  initializeStart,
  initializeSuccess,
  initializeFailure,
  googleLoginStart,
  googleLoginSuccess,
  googleLoginFailure,
  updateTokens,
  logout,
  clearError
} = authSlice.actions;
export default authSlice.reducer;
