import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity
} from 'react-native';
import { TransactionType } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import Button from '../common/Button';
import ZoomableImage from '../common/ZoomableImage';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { getCurrencySymbol } from '../../utils/currency';

interface TransactionFormData {
  amount: string;
  description?: string;
  purpose?: string;
  glCode: string;
  programCode: string;
  receiptUri?: string;
  recipient?: string;
  recipientText?: string; // Pre-formatted text for recipient display
  splitWith?: string[];
  splitWithText?: string; // Pre-formatted text for split with display
  localReceiptUri?: string; // New field for local receipt URI
  receiptUrl?: string; // New field for receipt URL
}

interface TransactionPreviewModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: TransactionFormData;
  type: TransactionType;
  loading?: boolean;
}

export default function TransactionPreviewModal({
  visible,
  onClose,
  onConfirm,
  data,
  type,
  loading = false
}: TransactionPreviewModalProps) {
  
  const getTransactionTitle = () => {
    switch (type) {
      case TransactionType.SPEND:
        return 'Cash Spent Transaction';
      case TransactionType.TRANSFER:
        return 'Cash Transfer Transaction';
      case TransactionType.RETURNED:
        return 'Cash Returned Transaction';
      default:
        return 'Transaction';
    }
  };

  const getTransactionIcon = () => {
    switch (type) {
      case TransactionType.SPEND:
        return '💳';
      case TransactionType.TRANSFER:
        return '💰';
      case TransactionType.RETURNED:
        return '↩️';
      default:
        return '💸';
    }
  };

  const currency = useSelector((state: RootState) => state.currency.value);
  const currencySymbol = getCurrencySymbol(currency);
  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount || '0');
    return `${currencySymbol}${num.toFixed(2)}`;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Review Transaction</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Transaction Overview */}
          <View style={styles.card}>
            <View style={styles.transactionHeader}>
              <Text style={styles.icon}>{getTransactionIcon()}</Text>
              <View style={styles.transactionInfo}>
                <Text style={styles.transactionTitle}>{getTransactionTitle()}</Text>
                <Text style={styles.amount}>{formatCurrency(data.amount)}</Text>
              </View>
            </View>
          </View>

          {/* Transaction Details */}
          <View style={styles.card}>
            <Text style={styles.sectionTitle}>Transaction Details</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Amount:</Text>
              <Text style={styles.detailValue}>{formatCurrency(data.amount)}</Text>
            </View>

            {data.description && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Description:</Text>
                <Text style={styles.detailValue}>{data.description}</Text>
              </View>
            )}

            {data.purpose && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Purpose:</Text>
                <Text style={styles.detailValue}>{data.purpose}</Text>
              </View>
            )}

            {data.recipient && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Recipient:</Text>
                <Text style={styles.detailValue}>{data.recipientText || data.recipient}</Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>GL Code:</Text>
              <Text style={styles.detailValue}>{data.glCode}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Program Code:</Text>
              <Text style={styles.detailValue}>{data.programCode}</Text>
            </View>

            {data.splitWithText.length > 0 &&
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Split With:</Text>
                <Text style={styles.detailValue}>{data.splitWithText}</Text>
              </View>
            }
          </View>

          {/* Receipt */}
          {(data.localReceiptUri || data.receiptUrl || data.receiptUri) && (
            <View style={styles.card}>
              <Text style={styles.sectionTitle}>Receipt</Text>
              <ZoomableImage 
                uri={data.localReceiptUri || data.receiptUrl || data.receiptUri} 
                style={styles.receiptImage}
              />
            </View>
          )}

          {/* Confirmation Message */}
          <View style={styles.confirmationCard}>
            <Text style={styles.confirmationText}>
              Please review all details above. Once submitted, this transaction will be sent for approval.
            </Text>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={onClose}
            variant="outline"
            style={styles.button}
            disabled={loading}
          />
          <Button
            title="Submit"
            onPress={onConfirm}
            variant="primary"
            style={styles.button}
            loading={loading}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.gray100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: Colors.gray600,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    fontSize: 32,
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  amount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: Colors.textPrimary,
    fontWeight: '600',
    flex: 2,
    textAlign: 'right',
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: Colors.gray100,
  },
  confirmationCard: {
    backgroundColor: Colors.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.primary + '20',
  },
  confirmationText: {
    fontSize: 14,
    color: Colors.primary,
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  button: {
    flex: 1,
  },
});
