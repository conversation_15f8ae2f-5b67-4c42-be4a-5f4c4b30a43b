import React, { useRef } from 'react';
import { ScrollView, Image, StyleSheet, NativeSyntheticEvent, NativeScrollEvent, Dimensions, TouchableWithoutFeedback } from 'react-native';

interface ZoomableImageProps {
  uri: string;
  style?: any;
  imageStyle?: any;
  minZoom?: number;
  maxZoom?: number;
}

const { width: screenWidth } = Dimensions.get('window');

export default function ZoomableImage({
  uri,
  style,
  imageStyle,
  minZoom = 1,
  maxZoom = 3,
}: ZoomableImageProps) {
  const scrollRef = useRef<ScrollView>(null);
  const lastTap = useRef<number>(0);

  // Double tap to zoom logic
  const handleDoubleTap = (event: any) => {
    const now = Date.now();
    if (lastTap.current && (now - lastTap.current) < 300) {
      // Double tap detected
      if (scrollRef.current) {
        scrollRef.current.scrollResponderZoomTo({
          x: 0,
          y: 0,
          width: screenWidth / maxZoom,
          height: screenWidth / maxZoom,
          animated: true,
        });
      }
    }
    lastTap.current = now;
  };

  return (
    <TouchableWithoutFeedback onPress={handleDoubleTap}>
      <ScrollView
        ref={scrollRef}
        style={[styles.container, style]}
        contentContainerStyle={{ flex: 1 }}
        maximumZoomScale={maxZoom}
        minimumZoomScale={minZoom}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bouncesZoom={true}
        centerContent={true}
      >
        <Image
          source={{ uri }}
          style={[styles.image, imageStyle]}
          resizeMode="contain"
        />
      </ScrollView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 200,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
}); 