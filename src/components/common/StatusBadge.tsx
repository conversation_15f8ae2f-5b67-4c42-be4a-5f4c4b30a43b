import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';

interface StatusBadgeProps {
  status: TransactionStatus;
  size?: 'small' | 'medium';
}

export default function StatusBadge({ status, size = 'medium' }: StatusBadgeProps) {
  const getStatusConfig = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.PENDING:
        return {
          backgroundColor: Colors.warning + '20',
          color: Colors.warning,
          text: 'Pending',
        };
      case TransactionStatus.APPROVED:
        return {
          backgroundColor: Colors.success + '20',
          color: Colors.success,
          text: 'Approved',
        };
      case TransactionStatus.REJECTED:
        return {
          backgroundColor: Colors.error + '20',
          color: Colors.error,
          text: 'Rejected',
        };
      case TransactionStatus.DRAFT:
        return {
          backgroundColor: Colors.gray300,
          color: Colors.gray700,
          text: 'Draft',
        };
      case TransactionStatus.CANCELLED:
        return {
          backgroundColor: Colors.gray200,
          color: Colors.gray500,
          text: 'Cancelled',
        };
      default:
        return {
          backgroundColor: Colors.gray300,
          color: Colors.gray700,
          text: 'Unknown',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <View
      style={[
        styles.badge,
        styles[size],
        { backgroundColor: config.backgroundColor },
      ]}
    >
      <Text
        style={[
          styles.text,
          styles[`${size}Text`],
          { color: config.color },
        ]}
      >
        {config.text}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  small: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  medium: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  smallText: {
    fontSize: 12,
  },
  mediumText: {
    fontSize: 14,
  },
});
