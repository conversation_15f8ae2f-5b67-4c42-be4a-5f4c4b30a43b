import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

interface NotificationBadgeProps {
  count: number;
  size?: 'small' | 'medium';
  color?: string;
  backgroundColor?: string;
}

export default function NotificationBadge({ 
  count, 
  size = 'medium',
  color = Colors.background,
  backgroundColor = Colors.error
}: NotificationBadgeProps) {
  if (count <= 0) return null;

  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <View
      style={[
        styles.badge,
        styles[size],
        { backgroundColor },
      ]}
    >
      <Text
        style={[
          styles.text,
          styles[`${size}Text`],
          { color },
        ]}
      >
        {displayCount}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  badge: {
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    alignSelf: 'flex-start',
    minWidth: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  small: {
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    minWidth: 16,
  },
  medium: {
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
  },
  text: {
    fontWeight: '700',
    textAlign: 'center',
  },
  smallText: {
    fontSize: 10,
  },
  mediumText: {
    fontSize: 12,
  },
});
