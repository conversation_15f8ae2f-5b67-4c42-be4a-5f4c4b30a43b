import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { 
  initializeStart, 
  initializeSuccess, 
  initializeFailure 
} from '../../store/slices/authSlice';
import { googleAuthService } from '../../services/googleAuth';
import { Colors } from '../../constants/colors';
import { debugLog } from '../../config/env';

interface AppInitializerProps {
  children: React.ReactNode;
}

export default function AppInitializer({ children }: AppInitializerProps) {
  const dispatch = useDispatch();
  const { isInitializing } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      debugLog('Initializing app and checking for stored session');
      dispatch(initializeStart());

      // Try to restore session from storage
      const authResult = await googleAuthService.restoreSession();
      
      if (authResult) {
        debugLog('Session restored successfully');
        dispatch(initializeSuccess({
          user: authResult.user,
          googleUser: authResult.googleUser,
          tokens: authResult.tokens,
        }));
      } else {
        debugLog('No valid session found');
        dispatch(initializeFailure());
      }
    } catch (error) {
      debugLog('App initialization error:', error);
      dispatch(initializeFailure());
    }
  };

  if (isInitializing) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.logo}>💰</Text>
          <Text style={styles.title}>THINK Wallet</Text>
          <Text style={styles.subtitle}>Think Global School</Text>
          <ActivityIndicator 
            size="large" 
            color={Colors.primary} 
            style={styles.loader}
          />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    padding: 40,
  },
  logo: {
    fontSize: 64,
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 40,
  },
  loader: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
});
