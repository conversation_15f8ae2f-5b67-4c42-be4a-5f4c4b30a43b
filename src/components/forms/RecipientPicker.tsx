import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import UserPicker from './UserPicker';

interface RecipientPickerProps {
  control: any;
  name: string;
  error?: string;
  onDisplayTextChange?: (text: string) => void; // Callback to provide formatted text
  editable?: boolean;
}

export default function RecipientPicker({
  control,
  name,
  error,
  onDisplayTextChange,
  editable = true,
}: RecipientPickerProps) {
  const { user: currentUser } = useSelector((state: RootState) => state.auth);
  if (!editable) {
    // Render as disabled/plain text
    return (
      <UserPicker
        control={control}
        name={name}
        label="Recipient"
        placeholder="Select recipient"
        error={error}
        multiplePick={false}
        fetchUsers={async () => []}
        searchUsers={async () => []}
        required={true}
        onDisplayTextChange={onDisplayTextChange}
        disabled={true}
      />
    );
  }

  // Fetch all users for recipient selection using the new API
  const fetchAllUsers = async (): Promise<User[]> => {
    try {
      const fetchedUsers = await apiService.getUsers(false); // only_student=false (all users)
      // Filter out current user
      return fetchedUsers.filter(u => u.id !== currentUser?.id);
    } catch (error) {
      console.error('Failed to fetch all users:', error);
      // Fallback to empty results if API fails
      return [];
    }
  };

  // Search users function for backend search
  const searchAllUsers = async (query: string): Promise<User[]> => {
    try {
      const searchResults = await apiService.searchUsers(query, false); // only_student=false (all users)
      // Filter out current user
      return searchResults.filter(u => u.id !== currentUser?.id);
    } catch (error) {
      console.error('Failed to search users:', error);
      // Fallback to empty results if search fails
      return [];
    }
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Recipient"
      placeholder="Select recipient"
      error={error}
      multiplePick={false}
      fetchUsers={fetchAllUsers}
      searchUsers={searchAllUsers}
      required={true}
      onDisplayTextChange={onDisplayTextChange}
    />
  );
}
