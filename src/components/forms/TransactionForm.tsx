import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSelector } from 'react-redux';
import { TransactionType } from '../../types/Transaction';
import { UserRole } from '../../types/User';
import { transactionSchema } from '../../utils/validation';
import { RootState } from '../../store/store';
import { getCurrencySymbol } from '../../utils/currency';
import Input from '../common/Input';
import Button from '../common/Button';
import ImagePicker from './ImagePicker';
import CategoryPicker from './CategoryPicker';
import StudentSplitPicker from './StudentSplitPicker';
import RecipientPicker from './RecipientPicker';
import { DropdownProvider } from '../../contexts/DropdownContext';

interface TransactionFormData {
  amount: string;
  glCode: string;
  programCode: string;
  splitWith: string[];
  description?: string;
  purpose?: string;
  receiptUri?: string;
  recipient?: string;
  recipientText?: string; // Pre-formatted text for recipient display
  splitWithText?: string; // Pre-formatted text for split with display
  localReceiptUri?: string | null; // Add localReceiptUri for preview modal
}

interface TransactionFormProps {
  type: TransactionType;
  onSubmit: (data: TransactionFormData) => void;
  onSaveDraft?: (data: TransactionFormData) => void;
  initialData?: Partial<TransactionFormData>;
  loading?: boolean;
  editableFields?: { [key: string]: boolean };
  hideSaveDraft?: boolean;
  onCancel?: () => void;
  buttonOrder?: string[]; // e.g. ["submit", "cancel"]
}

export default function TransactionForm({
  type,
  onSubmit,
  onSaveDraft,
  initialData,
  loading = false,
  editableFields = {},
  hideSaveDraft = false,
  onCancel,
  buttonOrder = ["cancel", "submit"],
}: TransactionFormProps) {
  const { user } = useSelector((state: RootState) => state.auth);
  const [splitWithText, setSplitWithText] = useState<string>('');
  const [recipientText, setRecipientText] = useState<string>('');
  const [imageUploading, setImageUploading] = useState(false); // NEW STATE
  const [localReceiptUri, setLocalReceiptUri] = useState<string | null>(null); // NEW STATE for local image
  const currency = useSelector((state: RootState) => state.currency.value);
  const currencySymbol = getCurrencySymbol(currency);
  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(transactionSchema(type)),
    defaultValues: {
      splitWith: [],
      ...initialData,
    },
    mode: 'onChange',
  });

  // Wrapper functions to include splitWithText and recipientText in form data
  const handleFormSubmit = async (data: TransactionFormData) => {
    onSubmit({ ...data, splitWithText, recipientText, localReceiptUri });
  };

  const handleFormSaveDraft = async (data: TransactionFormData) => {
    onSaveDraft({ ...data, splitWithText, recipientText, localReceiptUri });
  };

  return (
    <DropdownProvider>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
        <Controller
          control={control}
          name="amount"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Amount"
              value={value}
              onChangeText={onChange}
              keyboardType="numeric"
              placeholder={`0.00 (${currency})`}
              error={errors.amount?.message}
              required
              editable={editableFields.amount !== false}
              prefix={<Text style={{fontSize: 16, color: '#888'}}>{currencySymbol}</Text>}
              suffix={<Text style={{fontSize: 16, color: '#888'}}>{currency}</Text>}
            />
          )}
        />

        {type === TransactionType.TRANSFER && (
          <RecipientPicker
            control={control}
            name="recipient"
            error={errors.recipient?.message}
            onDisplayTextChange={setRecipientText}
            editable={editableFields.recipient !== false}
          />
        )}

        {type === TransactionType.TRANSFER && (
          <Controller
            control={control}
            name="purpose"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Purpose"
                value={value}
                onChangeText={onChange}
                placeholder="Purpose of cash transfer"
                multiline
                numberOfLines={3}
                error={errors.purpose?.message}
                editable={editableFields.purpose !== false}
              />
            )}
          />
        )}

        {(type === TransactionType.SPEND || type === TransactionType.RETURNED) && (
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Description"
                value={value}
                onChangeText={onChange}
                placeholder="What was this expense for?"
                multiline
                numberOfLines={3}
                error={errors.description?.message}
                required
                editable={editableFields.description !== false}
              />
            )}
          />
        )}

        <CategoryPicker
          control={control}
          name="glCode"
          label="GL Code"
          type="gl"
          error={errors.glCode?.message}
          editable={editableFields.glCode !== false}
        />

        <CategoryPicker
          control={control}
          name="programCode"
          label="Program Code"
          type="program"
          error={errors.programCode?.message}
          editable={editableFields.programCode !== false}
        />

        {type === TransactionType.SPEND && user?.role === UserRole.STUDENT &&
          <StudentSplitPicker
            control={control}
            name="splitWith"
            error={errors.splitWith?.message}
            onDisplayTextChange={setSplitWithText}
            editable={editableFields.splitWith !== false}
          />
        }

        <ImagePicker
          control={control}
          name="receiptUri"
          label="Receipt"
          required={type === TransactionType.SPEND}
          editable={editableFields.receiptUri !== false}
          onUploadingChange={setImageUploading}
          onLocalUriChange={setLocalReceiptUri} // Pass local image callback
        />

        <View style={styles.buttonContainer}>
        {!hideSaveDraft && onSaveDraft && (
            <Button
              title="Save Draft"
              onPress={handleSubmit(handleFormSaveDraft)}
              variant="outline"
              style={styles.button}
              loading={loading || imageUploading}
              disabled={imageUploading}
            />
          )}
          {buttonOrder.map((btn) => {
            if (btn === "submit") {
              return (
                <Button
                  key="submit"
                  title="Submit"
                  onPress={handleSubmit(handleFormSubmit)}
                  variant="primary"
                  style={styles.button}
                  loading={loading || imageUploading}
                  disabled={imageUploading}
                />
              );
            }
            if (btn === "cancel" && onCancel) {
              return (
                <Button
                  key="cancel"
                  title="Cancel"
                  onPress={onCancel}
                  variant="danger"
                  style={styles.button}
                  loading={loading}
                />
              );
            }
            return null;
          })}
          
        </View>
      </View>
    </ScrollView>
  </DropdownProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  form: {
    padding: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  button: {
    flex: 1,
  },
});
