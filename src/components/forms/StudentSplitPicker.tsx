import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import UserPicker from './UserPicker';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
  onDisplayTextChange?: (text: string) => void; // Callback to provide formatted text
  editable?: boolean;
}

export default function StudentSplitPicker({
  control,
  name,
  error,
  onDisplayTextChange,
  editable = true,
}: StudentSplitPickerProps) {
  const { user: currentUser } = useSelector((state: RootState) => state.auth);
  if (!editable) {
    // Render as disabled/plain text
    return (
      <UserPicker
        control={control}
        name={name}
        label="Split with other students (Equal split only)"
        placeholder="Select students to split with"
        error={error}
        multiplePick={true}
        fetchUsers={async () => []}
        searchUsers={async () => []}
        required={false}
        onDisplayTextChange={onDisplayTextChange}
        disabled={true}
      />
    );
  }

  // Fetch students for split selection using the new API
  const fetchStudents = async (): Promise<User[]> => {
    try {
      const fetchedStudents = await apiService.getUsers(true); // only_student=true
      // Filter out current user and only keep students
      return fetchedStudents.filter(
        u => u.id !== currentUser?.id && u.groups?.some(g => g.name === 'Student')
      );
    } catch (error) {
      console.error('Failed to fetch students:', error);
      // Fallback to empty results if API fails
      return [];
    }
  };

  // Search students function for backend search
  const searchStudents = async (query: string): Promise<User[]> => {
    try {
      const searchResults = await apiService.searchUsers(query, true); // only_student=true
      // Filter out current user and only keep students
      return searchResults.filter(
        u => u.id !== currentUser?.id && u.groups?.some(g => g.name === 'Student')
      );
    } catch (error) {
      console.error('Failed to search students:', error);
      // Fallback to empty results if search fails
      return [];
    }
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Split with other students (Equal split only)"
      placeholder="Select students to split with"
      error={error}
      multiplePick={true}
      fetchUsers={fetchStudents}
      searchUsers={searchStudents}
      required={false}
      onDisplayTextChange={onDisplayTextChange}
    />
  );
}
