import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { STORAGE_KEYS, debugLog } from '../config/env';
import { User } from '../types/User';
import { GoogleUser } from './googleAuth';

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface StoredUserData {
  user: User;
  googleUser: GoogleUser;
  tokens: AuthTokens;
  lastLogin: string;
}

class StorageService {
  private static instance: StorageService;

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  // Secure storage methods
  private async setSecureItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Fallback to localStorage for web
        localStorage.setItem(key, value);
      } else {
        await SecureStore.setItemAsync(key, value);
      }
      debugLog(`Stored item: ${key}`);
    } catch (error) {
      console.error(`Error storing ${key}:`, error);
      throw error;
    }
  }

  private async getSecureItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Fallback to localStorage for web
        return localStorage.getItem(key);
      } else {
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.error(`Error retrieving ${key}:`, error);
      return null;
    }
  }

  private async deleteSecureItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key);
      } else {
        await SecureStore.deleteItemAsync(key);
      }
      debugLog(`Deleted item: ${key}`);
    } catch (error) {
      console.error(`Error deleting ${key}:`, error);
    }
  }

  // Token management
  async storeTokens(tokens: AuthTokens): Promise<void> {
    await this.setSecureItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.accessToken);
    await this.setSecureItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken);
  }

  async getTokens(): Promise<AuthTokens | null> {
    try {
      const accessToken = await this.getSecureItem(STORAGE_KEYS.ACCESS_TOKEN);
      const refreshToken = await this.getSecureItem(STORAGE_KEYS.REFRESH_TOKEN);

      if (accessToken && refreshToken) {
        return { accessToken, refreshToken };
      }
      return null;
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return null;
    }
  }

  async clearTokens(): Promise<void> {
    await this.deleteSecureItem(STORAGE_KEYS.ACCESS_TOKEN);
    await this.deleteSecureItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  // User data management
  async storeUserData(userData: StoredUserData): Promise<void> {
    try {
      const dataString = JSON.stringify(userData);
      await this.setSecureItem(STORAGE_KEYS.USER_DATA, dataString);
      debugLog('User data stored successfully');
    } catch (error) {
      console.error('Error storing user data:', error);
      throw error;
    }
  }

  async getUserData(): Promise<StoredUserData | null> {
    try {
      const dataString = await this.getSecureItem(STORAGE_KEYS.USER_DATA);
      if (dataString) {
        const userData = JSON.parse(dataString) as StoredUserData;
        debugLog('User data retrieved successfully');
        return userData;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  }

  async clearUserData(): Promise<void> {
    await this.deleteSecureItem(STORAGE_KEYS.USER_DATA);
    debugLog('User data cleared');
  }

  // Google user data management
  async storeGoogleUserData(googleUser: GoogleUser): Promise<void> {
    try {
      const dataString = JSON.stringify(googleUser);
      await this.setSecureItem(STORAGE_KEYS.GOOGLE_USER_DATA, dataString);
      debugLog('Google user data stored successfully');
    } catch (error) {
      console.error('Error storing Google user data:', error);
      throw error;
    }
  }

  async getGoogleUserData(): Promise<GoogleUser | null> {
    try {
      const dataString = await this.getSecureItem(STORAGE_KEYS.GOOGLE_USER_DATA);
      if (dataString) {
        return JSON.parse(dataString) as GoogleUser;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving Google user data:', error);
      return null;
    }
  }

  // Complete logout - clear all data
  async clearAllData(): Promise<void> {
    await Promise.all([
      this.clearTokens(),
      this.clearUserData(),
      this.deleteSecureItem(STORAGE_KEYS.GOOGLE_USER_DATA),
    ]);
    debugLog('All stored data cleared');
  }

  // Check if user is logged in
  async isLoggedIn(): Promise<boolean> {
    const tokens = await this.getTokens();
    const userData = await this.getUserData();
    return !!(tokens && userData);
  }
}

export const storageService = StorageService.getInstance();
