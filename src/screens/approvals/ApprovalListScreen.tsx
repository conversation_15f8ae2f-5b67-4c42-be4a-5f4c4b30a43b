import React, {useEffect, useState} from 'react';
import {Alert, FlatList, Image, Modal, Pressable, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import {RootState} from '../../store/store';
import {Transaction} from '../../types/Transaction';
import {Colors} from '../../constants/colors';
import {canApproveTransactions} from '../../utils/permissions';
import ApprovalCard from '../../components/cards/ApprovalCard';
import {apiService} from '../../services/apiService';
import {extractApiErrorMessage} from '../../utils/validation';
import {mapTransactionResponse} from '../../utils/mapping';
import ZoomableImage from '../../components/common/ZoomableImage';

export default function ApprovalListScreen() {
    const navigation = useNavigation<any>();
    const route = useRoute<any>();
    const {user} = useSelector((state: RootState) => state.auth);
    const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>([]);
    const [loading, setLoadingState] = useState<string | null>(null);
    const [fetchLoading, setFetchLoading] = useState(true);
    const [receiptModalVisible, setReceiptModalVisible] = useState(false);
    const [selectedReceipt, setSelectedReceipt] = useState<{ uri: string, id: string } | null>(null);

    // Get pending transactions from navigation params if available
    const navigationPendingTransactions = route.params?.pendingTransactions;

    // Function to fetch pending transactions from API
    const fetchPendingTransactions = async () => {
        if (!user || !canApproveTransactions(user.role, user.isAdvisor)) {
            return;
        }

        try {
            setFetchLoading(true);
            const transactions = await apiService.getPendingTransactions();

            // Use the mapping utility to convert API response to local Transaction format
            const convertedTransactions: Transaction[] = transactions.map(mapTransactionResponse);

            // Sort by newest first (created_at descending)
            const sortedTransactions = convertedTransactions.sort((a, b) =>
                new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );

            setPendingTransactions(sortedTransactions);
        } catch (error) {
            console.error('Error fetching pending transactions:', error);
        } finally {
            setFetchLoading(false);
        }
    };

    // Fetch pending transactions on component mount
    useEffect(() => {
        // Use navigation params if available, otherwise fetch from API
        if (navigationPendingTransactions && navigationPendingTransactions.length >= 0) {
            setPendingTransactions(navigationPendingTransactions);
            setFetchLoading(false);
        } else {
            fetchPendingTransactions();
        }
    }, [user, navigationPendingTransactions]);

    if (!user || !canApproveTransactions(user.role, user.isAdvisor)) {
        return (
            <View style={styles.container}>
                <View style={styles.errorContainer}>
                    <Text style={styles.errorTitle}>No Access</Text>
                    <Text style={styles.errorMessage}>
                        You don't have permission to approve transactions.
                    </Text>
                </View>
            </View>
        );
    }

    const handleApprove = async (transactionId: string) => {
        setLoadingState(transactionId);
        try {
            await apiService.approveTransaction(transactionId);

            Alert.alert('Success', 'Transaction approved successfully');

            // Refresh the list to get updated data
            await fetchPendingTransactions();
        } catch (error: any) {
            console.error('Approval error:', error);
            Alert.alert('Error', extractApiErrorMessage(error));
        } finally {
            setLoadingState(null);
        }
    };

    const handleReject = (transactionId: string) => {
        Alert.prompt(
            'Reject Transaction',
            'Please provide a reason for rejection:',
            [
                {text: 'Cancel', style: 'cancel'},
                {
                    text: 'Reject',
                    style: 'destructive',
                    onPress: async (reason) => {
                        if (!reason?.trim()) {
                            Alert.alert('Error', 'Please provide a reason for rejection');
                            return;
                        }

                        setLoadingState(transactionId);
                        try {
                            await apiService.rejectTransaction(transactionId, reason);

                            Alert.alert('Success', 'Transaction rejected');

                            // Refresh the list to get updated data
                            await fetchPendingTransactions();
                        } catch (error: any) {
                            console.error('Rejection error:', error);
                            Alert.alert('Error', extractApiErrorMessage(error));
                        } finally {
                            setLoadingState(null);
                        }
                    },
                },
            ],
            'plain-text'
        );
    };

    const handleViewReceipt = (transactionId: string) => {
        const tx = pendingTransactions.find(t => t.id === transactionId);
        if (!tx) return;
        const uri = tx.receiptUri || tx.receiptUrl;
        if (!uri) return;
        setSelectedReceipt({uri, id: transactionId});
        setReceiptModalVisible(true);
    };

    const renderApprovalCard = ({item}: { item: any }) => (
        <ApprovalCard
            transaction={item}
            onApprove={handleApprove}
            onReject={handleReject}
            onViewReceipt={handleViewReceipt}
            loading={loading === item.id}
        />
    );

    const renderEmptyState = () => (
        <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>✅</Text>
            <Text style={styles.emptyTitle}>All caught up!</Text>
            <Text style={styles.emptySubtitle}>
                No transactions pending approval at the moment.
            </Text>
        </View>
    );

    return (
        <View style={styles.container}>
            {/* Modal for viewing receipt */}
            <Modal
                visible={receiptModalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setReceiptModalVisible(false)}
            >
                <Pressable style={styles.modalOverlay} onPress={() => setReceiptModalVisible(false)}>
                    <Pressable style={styles.modalContent} onPress={(e) => e.stopPropagation()}>
                        <View style={styles.modalHeader}>
                            <View style={styles.modalClose}/>
                            <Text style={styles.modalTitle}>Receipt</Text>
                            <Pressable style={styles.modalClose} onPress={() => setReceiptModalVisible(false)}>
                                <Text style={styles.modalCloseText}>✕</Text>
                            </Pressable>
                        </View>
                        {selectedReceipt && (
                            <ZoomableImage
                                uri={selectedReceipt.uri}
                                style={styles.receiptImage}
                            />
                        )}
                    </Pressable>
                </Pressable>
            </Modal>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Text style={styles.backButtonText}>← Back</Text>
                </TouchableOpacity>
                <Text style={styles.title}>Approvals</Text>
                <View style={styles.badge}>
                    <Text style={styles.badgeText}>{pendingTransactions.length}</Text>
                </View>
            </View>

            {/* Subtitle */}
            <View style={styles.subtitle}>
                <Text style={styles.subtitleText}>
                    {pendingTransactions.length === 0
                        ? 'No pending approvals'
                        : `${pendingTransactions.length} transaction${pendingTransactions.length === 1 ? '' : 's'} awaiting approval`
                    }
                </Text>
            </View>

            {/* Approvals List */}
            {fetchLoading ? (
                <View style={styles.loadingContainer}>
                    <Text style={styles.loadingText}>Loading pending transactions...</Text>
                </View>
            ) : (
                <FlatList
                    data={pendingTransactions}
                    renderItem={renderApprovalCard}
                    keyExtractor={(item) => item.id}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={styles.listContainer}
                    ListEmptyComponent={renderEmptyState}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.surface,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        paddingTop: 60,
        backgroundColor: Colors.background,
    },
    backButton: {
        padding: 8,
    },
    backButtonText: {
        fontSize: 16,
        color: Colors.primary,
        fontWeight: '600',
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        color: Colors.textPrimary,
    },
    badge: {
        backgroundColor: Colors.warning,
        borderRadius: 12,
        paddingHorizontal: 8,
        paddingVertical: 4,
        minWidth: 24,
        alignItems: 'center',
    },
    badgeText: {
        color: Colors.background,
        fontSize: 14,
        fontWeight: 'bold',
    },
    subtitle: {
        backgroundColor: Colors.background,
        paddingHorizontal: 20,
        paddingBottom: 16,
    },
    subtitleText: {
        fontSize: 16,
        color: Colors.textSecondary,
    },
    listContainer: {
        paddingVertical: 8,
        flexGrow: 1,
    },
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
    },
    emptyIcon: {
        fontSize: 64,
        marginBottom: 16,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: Colors.textPrimary,
        marginBottom: 8,
    },
    emptySubtitle: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: Colors.textPrimary,
        marginBottom: 16,
    },
    errorMessage: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
    },
    loadingText: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.6)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: Colors.background,
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
        maxWidth: '90%',
        maxHeight: '80%',
    },
    modalHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 12,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: Colors.textPrimary,
        alignSelf: 'flex-start',
    },
    modalClose: {
        padding: 8,
    },
    modalCloseText: {
        fontSize: 24,
        color: Colors.textPrimary,
        fontWeight: 'bold',
    },
    receiptImage: {
        width: 300,
        height: 400,
        borderRadius: 8,
        backgroundColor: Colors.gray100,
    },
});
