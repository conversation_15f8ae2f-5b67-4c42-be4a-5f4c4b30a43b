import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView, Image } from 'react-native';
import { useDispatch } from 'react-redux';
import { googleLoginStart, googleLoginSuccess, googleLoginFailure } from '../../store/slices/authSlice';
import { googleAuthService } from '../../services/googleAuth';
import Button from '../../components/common/Button';
import GoogleSignInButton from '../../components/common/GoogleSignInButton';
import { Colors } from '../../constants/colors';
import { debugLog } from '../../config/env';


export default function LoginScreen() {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    setLoading(true);
    dispatch(googleLoginStart());

    try {
      debugLog('Starting Google Sign-In from UI');
      const authResult = await googleAuthService.signIn();

      if (authResult) {
        debugLog('Google Sign-In successful, dispatching success action');
        dispatch(googleLoginSuccess({
          user: authResult.user,
          googleUser: authResult.googleUser,
          tokens: authResult.tokens,
        }));
      } else {
        debugLog('Google Sign-In was cancelled by user');
        dispatch(googleLoginFailure('Google sign-in was cancelled'));
      }
    } catch (error: any) {
      debugLog('Google Sign-In error:', error);
      const errorMessage = error.message || 'Failed to sign in with Google';
      dispatch(googleLoginFailure(errorMessage));

      Alert.alert(
        'Sign-In Error',
        errorMessage + '\n\nPlease ensure you use a Think Global School email address (@thinkglobalschool.com)'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.header}>
        <Image source={require('../../../assets/icon.png')} style={styles.logo} resizeMode="cover" />
        <Text style={styles.title}>THINK Wallet</Text>
        <Text style={styles.subtitle}>Think Global School</Text>
        <Text style={styles.description}>
          Secure expense management for students and staff
        </Text>
      </View>

      <View style={styles.authSection}>
        <Text style={styles.authTitle}>Sign in to continue</Text>
        <Text style={styles.authSubtitle}>
          Use your Think Global School Google account
        </Text>

        <GoogleSignInButton
          onPress={handleGoogleSignIn}
          loading={loading}
        />
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Secure • Private • Think Global School
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
    minHeight: '100%',
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logo: {
    width: 256,
    height: 256,
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: Colors.textTertiary,
    textAlign: 'center',
    marginTop: 8,
  },
  authSection: {
    marginBottom: 48,
    gap: 16,
  },
  authTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
  },
  authSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
  },

  staffButton: {
    borderColor: Colors.gray300,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    fontWeight: '600',
  },
  footerSubtext: {
    fontSize: 12,
    color: Colors.textTertiary,
    textAlign: 'center',
    marginTop: 4,
  },
});
