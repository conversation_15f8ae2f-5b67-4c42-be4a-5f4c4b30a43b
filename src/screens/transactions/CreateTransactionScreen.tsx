import React, {useState} from 'react';
import {Alert, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import {RootState} from '../../store/store';
import {TransactionType} from '../../types/Transaction';
import {Colors} from '../../constants/colors';
import TransactionForm from '../../components/forms/TransactionForm';
import TransactionPreviewModal from '../../components/modals/TransactionPreviewModal';
import {apiService, TransactionCreateRequest} from '../../services/apiService';
import {extractApiErrorMessage} from '../../utils/validation';

export default function CreateTransactionScreen() {
    const navigation = useNavigation();
    const route = useRoute<any>();
    const {user} = useSelector((state: RootState) => state.auth);
    const currency = useSelector((state: RootState) => state.currency.value);
    const [loading, setLoading] = useState(false);
    const [showPreviewModal, setShowPreviewModal] = useState(false);
    const [previewData, setPreviewData] = useState<any>(null);
    const selectedType = route.params?.type || TransactionType.SPEND

    if (!user) return null;

    const handleSubmit = (data: any) => {
        // Show preview modal instead of directly submitting
        setPreviewData(data);
        setShowPreviewModal(true);
    };

    const handleFinalSubmit = async () => {
        if (!previewData) return;

        setLoading(true);
        try {
            const transactionRequest: TransactionCreateRequest = {
                submit: true,
                type: selectedType,
                amount: previewData.amount,
                currency: currency,
                notes: selectedType === TransactionType.TRANSFER ? previewData.purpose : previewData.description,
                gl_code: previewData.glCode,
                program_code: previewData.programCode,
                receipt: previewData.receiptUri || undefined,
                related_user: previewData.recipient,
                split_with: previewData.splitWith || [],
            };

            await apiService.createTransaction(transactionRequest);

            // Close modal and show success
            setShowPreviewModal(false);
            Alert.alert(
                'Success',
                'Transaction submitted successfully',
                [{text: 'OK', onPress: () => navigation.goBack()}]
            );
        } catch (error: any) {
            console.error('Transaction submission error:', error);
            Alert.alert('Error', extractApiErrorMessage(error));
        } finally {
            setLoading(false);
        }
    };

    const handleClosePreview = () => {
        setShowPreviewModal(false);
        setPreviewData(null);
    };

    const handleSaveDraft = async (data: any) => {
        setLoading(true);
        try {
            const transactionRequest: TransactionCreateRequest = {
                submit: false,
                type: selectedType,
                amount: data.amount,
                currency: currency,
                notes: selectedType === TransactionType.TRANSFER ? data.purpose : data.description,
                gl_code: data.glCode,
                program_code: data.programCode,
                receipt: data.receiptUri || undefined,
                related_user: data.recipient,
                split_with: data.splitWith || [],
            };

            await apiService.createTransaction(transactionRequest);

            Alert.alert(
                'Draft Saved',
                'Transaction saved as draft',
                [{text: 'OK', onPress: () => navigation.goBack()}]
            );
        } catch (error: any) {
            console.error('Draft save error:', error);
            Alert.alert('Error', extractApiErrorMessage(error));
        } finally {
            setLoading(false);
        }
    };

    const getFormTitle = () => {
        switch (selectedType) {
            case TransactionType.SPEND:
                return 'Log Cash Spent';
            case TransactionType.TRANSFER:
                return 'Log Cash Given';
            case TransactionType.RETURNED:
                return 'Log Cash Returned';
            default:
                return 'New Transaction';
        }
    };

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={styles.backButton}>← Back</Text>
                </TouchableOpacity>
                <Text style={styles.title}>{getFormTitle()}</Text>
                <View style={styles.placeholder}/>
            </View>

            {/* Transaction Form */}
            <TransactionForm
                type={selectedType}
                onSubmit={handleSubmit}
                onSaveDraft={handleSaveDraft}
                loading={loading && !showPreviewModal}
            />

            {/* Preview Modal */}
            {previewData && (
                <TransactionPreviewModal
                    visible={showPreviewModal}
                    onClose={handleClosePreview}
                    onConfirm={handleFinalSubmit}
                    data={previewData}
                    type={selectedType}
                    loading={loading}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.surface,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        paddingTop: 60,
        backgroundColor: Colors.background,
    },
    backButton: {
        color: Colors.primary,
        fontSize: 16,
        fontWeight: '600',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.textPrimary,
    },
    placeholder: {
        width: 50,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
    },
    errorTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: Colors.textPrimary,
        marginBottom: 16,
    },
    errorMessage: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
    },
});
