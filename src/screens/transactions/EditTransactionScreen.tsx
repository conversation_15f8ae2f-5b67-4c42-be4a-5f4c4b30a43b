import React, {useState} from 'react';
import {Alert, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {TransactionStatus} from '../../types/Transaction';
import {Colors} from '../../constants/colors';
import TransactionForm from '../../components/forms/TransactionForm';
import TransactionPreviewModal from '../../components/modals/TransactionPreviewModal';
import {apiService} from '../../services/apiService';
import {extractApiErrorMessage} from '../../utils/validation';

export default function EditTransactionScreen() {
    const navigation = useNavigation<any>();
    const route = useRoute<any>();
    const transaction = route.params?.transaction;
    const [loading, setLoading] = useState(false);
    const [showPreviewModal, setShowPreviewModal] = useState(false);
    const [previewData, setPreviewData] = useState<any>(null);

    if (!transaction) return null;

    // Prefill form fields from transaction
    const initialData = {
        amount: String(transaction.amount),
        glCode: transaction.glCode || transaction.gl_code?.code || '',
        programCode: transaction.programCode || transaction.program_code?.code || '',
        splitWith: transaction.splitWith || [],
        description: transaction.description || transaction.notes || '',
        purpose: transaction.purpose || '',
        receiptUri: transaction.receiptUrl || transaction.receiptUri || transaction.receipt || '',
        recipient: transaction.recipient || transaction.related_user || '',
    };

    // Only allow editing description, amount, and image
    const editableFields = {
        amount: true,
        description: true,
        receiptUri: true,
        glCode: true, // allow editing
        programCode: true, // allow editing
        splitWith: false,
        purpose: false,
        recipient: false,
    };

    const handleSubmit = (data: any) => {
        setPreviewData(data);
        setShowPreviewModal(true);
    };

    const handleFinalSubmit = async () => {
        if (!previewData) return;
        setLoading(true);
        try {
            // Only include changed fields
            const patchData: any = {};
            if (String(previewData.amount) !== String(initialData.amount)) patchData.amount = previewData.amount;
            if ((previewData.description || '') !== (initialData.description || '')) patchData.notes = previewData.description;
            if ((previewData.receiptUri || '') !== (initialData.receiptUri || '')) patchData.receipt = previewData.receiptUri;
            if ((previewData.glCode || '') !== (initialData.glCode || '')) patchData.gl_code = previewData.glCode;
            if ((previewData.programCode || '') !== (initialData.programCode || '')) patchData.program_code = previewData.programCode;
            // Always include submit if status is DRAFT
            if (transaction.status === TransactionStatus.DRAFT) patchData.submit = true;
            await apiService.patchTransaction(transaction.id, patchData);
            setShowPreviewModal(false);
            Alert.alert('Success', 'Transaction updated successfully', [
                {text: 'OK', onPress: () => navigation.pop(2)},
            ]);
        } catch (error: any) {
            Alert.alert('Error', extractApiErrorMessage(error));
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = async () => {
        setLoading(true);
        try {
            await apiService.patchTransaction(transaction.id, {
                cancel: true,
            });
            Alert.alert('Cancelled', 'Transaction has been cancelled.', [
                {text: 'OK', onPress: () => navigation.pop(2)},
            ]);
        } catch (error: any) {
            Alert.alert('Error', extractApiErrorMessage(error));
        } finally {
            setLoading(false);
        }
    };

    const handleClosePreview = () => {
        setShowPreviewModal(false);
        setPreviewData(null);
    };

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={styles.backButton}>← Back</Text>
                </TouchableOpacity>
                <Text style={styles.title}>Edit Transaction</Text>
                <View style={styles.placeholder}/>
            </View>
            <TransactionForm
                type={transaction.type}
                onSubmit={handleSubmit}
                initialData={initialData}
                loading={loading && !showPreviewModal}
                editableFields={editableFields}
                hideSaveDraft={true}
                onCancel={handleCancel}
                buttonOrder={["cancel", "submit"]}
            />
            {previewData && (
                <TransactionPreviewModal
                    visible={showPreviewModal}
                    onClose={handleClosePreview}
                    onConfirm={handleFinalSubmit}
                    data={previewData}
                    type={transaction.type}
                    loading={loading}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.surface,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        paddingTop: 60,
        backgroundColor: Colors.background,
    },
    backButton: {
        color: Colors.primary,
        fontSize: 16,
        fontWeight: '600',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: Colors.textPrimary,
    },
    placeholder: {
        width: 50,
    },
}); 