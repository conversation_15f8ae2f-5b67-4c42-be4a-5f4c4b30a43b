import { UserRole } from '../types/User';

export const ROLE_PERMISSIONS = {
  [UserRole.STUDENT]: {
    canCreateTransactions: true,
    canApproveTransactions: false,
    canViewAllTransactions: false,
  },
  [UserRole.STAFF]: {
    canCreateTransactions: true,
    canApproveTransactions: true, // if isAdvisor
    canViewAllTransactions: true, // if isAdvisor
  },
  [UserRole.ADMIN]: {
    canCreateTransactions: true,
    canApproveTransactions: true,
    canViewAllTransactions: true,
  },
};
