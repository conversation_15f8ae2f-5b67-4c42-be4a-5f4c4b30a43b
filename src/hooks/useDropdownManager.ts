import { useRef, useCallback } from 'react';

export interface DropdownManager {
  registerDropdown: (id: string, closeCallback: () => void) => void;
  unregisterDropdown: (id: string) => void;
  closeAllExcept: (exceptId: string) => void;
  closeAll: () => void;
}

export function useDropdownManager(): DropdownManager {
  const dropdownsRef = useRef<Map<string, () => void>>(new Map());

  const registerDropdown = useCallback((id: string, closeCallback: () => void) => {
    dropdownsRef.current.set(id, closeCallback);
  }, []);

  const unregisterDropdown = useCallback((id: string) => {
    dropdownsRef.current.delete(id);
  }, []);

  const closeAllExcept = useCallback((exceptId: string) => {
    dropdownsRef.current.forEach((closeCallback, id) => {
      if (id !== exceptId) {
        closeCallback();
      }
    });
  }, []);

  const closeAll = useCallback(() => {
    dropdownsRef.current.forEach((closeCallback) => {
      closeCallback();
    });
  }, []);

  return {
    registerDropdown,
    unregisterDropdown,
    closeAllExcept,
    closeAll,
  };
}
