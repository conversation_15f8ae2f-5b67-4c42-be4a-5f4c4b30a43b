import 'dotenv/config';

export default {
  expo: {
    name: "THINK Wallet",
    slug: "think-wallet",
    version: "1.0.0",
    icon: "./assets/icon.png",
    orientation: "portrait",
    userInterfaceStyle: "light",
    extra: {
      apiBaseUrl: process.env.API_BASE_URL || 'https://think-wallet-f490339730c6.herokuapp.com',
      googleIosClientId: process.env.GOOGLE_IOS_CLIENT_ID || '450493643192-1d6j385s208d5d0sofkmd8lr1tvda9pu.apps.googleusercontent.com',
      appEnv: process.env.APP_ENV,
      debugMode: process.env.DEBUG_MODE,
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "org.thinkglobalschool.wallet",
      googleServicesFile: "./GoogleService-Info.plist", // Add your iOS Google Services file
      infoPlist: {
        CFBundleURLTypes: [
          {
            CFBundleURLName: "google",
            CFBundleURLSchemes: ["org.thinkglobalschool.wallet"]
          }
        ],
        NSCameraUsageDescription: "This app needs access to camera to take receipt photos.",
        NSPhotoLibraryUsageDescription: "This app needs access to photo library to select receipt images."
      }
    },
    android: {
      permissions: [
        "CAMERA",
        "READ_EXTERNAL_STORAGE",
        "WRITE_EXTERNAL_STORAGE"
      ]
    },
    web: {},
    scheme: "think-wallet",
    plugins: [
      "@react-native-google-signin/google-signin",
      "expo-secure-store",
      [
        "expo-image-picker",
        {
          photosPermission: "The app accesses your photos to let you select receipt images.",
          cameraPermission: "The app accesses your camera to let you take receipt photos."
        }
      ]
    ]
  }
};
